using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.MailCenter.Applications.Models.ContentManage;
using iPlatformExtension.MailCenter.Applications.Queries.ContentManage;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.MailCenterRepository.Interface;
using MediatR;
using System.Collections.Generic;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Enum;
using static iPlatformExtension.Model.Enum.SysEnum;

namespace iPlatformExtension.MailCenter.Applications.Handlers.ContentManage
{
    /// <summary>
    /// 获取发件阅读人列表
    /// </summary>
    /// <param name="mySql"></param>
    /// <param name="httpContextAccessor"></param>
    /// <param name="msSql"></param>
    public class GetMyReadListBySendHandler(IFreeSql<MailCenterFreeSql> mySql, IHttpContextAccessor httpContextAccessor, IFreeSql<PlatformFreeSql> msSql) : IRequestHandler<GetMyReadListBySendQuery, PageResult<ReadListDto>>
    {
        public async Task<PageResult<ReadListDto>> Handle(GetMyReadListBySendQuery request, CancellationToken cancellationToken)
        {
            var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
            var lst = await mySql.Select<MailReaderList, MailSend, MailSendFlow>()
                .LeftJoin(o => o.t1.MailId == o.t2.MailId)
                .LeftJoin(o => o.t2.MailId == o.t3.MailId)
                 .Where(o => o.t1.UserId == userId && o.t1.MailType == SysEnum.MailType.Send.ToString()
                 && o.t1.Status == (int)ReaderStatusEnum.ToRead && o.t2.Status == SendStatusType.Sent.GetHashCode())
                 .WhereIf(!string.IsNullOrEmpty(request.Search),
                     o => o.t2.MailNo.Contains(request.Search) || o.t2.MailSubject.Contains(request.Search) ||
                          o.t2.MailFrom.Contains(request.Search))
                 .Count(out long totalCount).OrderByDescending(o => o.t2.MailNo)
                 .Distinct()
                 .Take(request.PageSize).Skip(request.PageSize * (request.PageIndex - 1))
                 .ToListAsync<ReadListDto>(o =>
                         new ReadListDto
                         {
                             ReadId = o.t1.Id,
                             ReadTime = o.t1.UpdateTime,
                             MailId = o.t1.MailId,
                             MailDate = o.t2.MailDate,
                             MailFrom = o.t2.MailFrom,
                             MailNo = o.t2.MailNo,
                             MailTo = o.t2.MailTo,
                             MailSubject = o.t2.MailSubject,
                             Status = o.t2.Status,
                             UndertakeUserId = o.t3.UndertakeUserId,
                             HasPersonalTag = mySql.Select<MailTagList, MailTag>().LeftJoin(m => m.t1.TagId == m.t2.Id).Any(m=>m.t1.MailId ==o.t2.MailId && m.t2.MailType == SysEnum.MailType.Send.ToString() && m.t2.UserId == userId),
                             AuditUserId = o.t3.AuditUser,
                             MailPriority = o.t2.MailPriority
                         }
                     , cancellationToken);

            var userList = await msSql.Select<SysUserInfo>().Where(u => lst.Any(o => o.IgnoreBy == u.UserId || o.SortBy == u.UserId || o.UndertakeUserId == u.UserId || o.AuditUserId == u.UserId)).ToListAsync((u => new { UserName = u.CnName, UserId = u.UserId }), cancellationToken).ConfigureAwait(false);
            lst.ForEach(o =>
            {
                o.IgnoreByUserName = userList.FirstOrDefault(u => u.UserId == o.IgnoreBy)?.UserName;
                o.SortByUserName = userList.FirstOrDefault(u => u.UserId == o.SortBy)?.UserName;
                o.UndertakeUserName = userList.FirstOrDefault(u => u.UserId == o.UndertakeUserId)?.UserName;
                o.AuditUser = userList.FirstOrDefault(u => u.UserId == o.AuditUserId)?.UserName;
            });

            return new PageResult<ReadListDto>()
            {
                Data = lst,
                Page = request.PageIndex,
                PageSize = request.PageSize,
                Total = totalCount
            };
        }
    }
}
