using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Models.Process;
using iPlatformExtension.MailCenter.Applications.Queries.Process;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.Repository.Interface;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Process;

/// <summary>
/// 获取待处理列表
/// </summary>
internal sealed class CollectionProcessListQueryHandler(IFreeSql<MailCenterFreeSql> freeSql, IMediator mediator, IHttpContextAccessor content, IUserInfoRepository userInfoRepository) : IRequestHandler<CollectionProcessListQuery, IEnumerable<CollectionProcessListDto>>
{
    public async Task<IEnumerable<CollectionProcessListDto>> Handle(CollectionProcessListQuery request, CancellationToken cancellationToken)
    {
        var userId = content.HttpContext?.User.GetUserID() ?? string.Empty;
        var privateList = (request.CheckedPrivateList?.Contains("") | request.CheckedPrivateList?.Contains(null));
        var Action = new[] { "收件办理", "收件分拣", "结束" };
        var mailReceives = freeSql.Select<MailReceive, MailReceiveFlow, FlowRecord, FlowRecord, FlowPrivateList>()
            .LeftJoin(it => it.t1.MailId == it.t2.MailId)
            .LeftJoin(it => it.t2.MailId == it.t3.MailId && it.t3.IsCurrent == SysEnum.Status.Enable.GetHashCode())
            .LeftJoin(it => it.t3.PreRecordId == it.t4.Id)
            .LeftJoin(it => it.t1.MailId == it.t5.MailId)
            .Where(it => it.t3 != null)
            .Where(it => it.t3.AuditUser == userId)
            .WithLock()
            .WhereIf(privateList is not null && privateList == true, it => request.CheckedPrivateList!.Contains(it.t5.PrivateId) || it.t5 == null)
            .WhereIf(privateList == false, it => request.CheckedPrivateList!.Contains(it.t5.PrivateId))
            .WhereIf(request.Search is not null && !Action.Contains(request.Search), it => it.t1.MailNo == request.Search || it.t1.MailSubject.Contains(request.Search) || it.t1.MailFrom.Contains(request.Search))
            .WhereIf(Action.Contains(request.Search), it => it.t3.CurNodeId == request.Search.Replace("收件办理", SysEnum.MailFlowAction.Handle.ToString()).Replace("收件分拣", SysEnum.MailFlowAction.Allot.ToString()).Replace("结束", SysEnum.MailFlowAction.End.ToString()))
            .Page(request.PageIndex!.Value, request.PageSize!.Value).Count(out var count);
        //排序
        mailReceives = request.Sort is null ? mailReceives.OrderByDescending(it => it.t4.AuditTime) : string.Equals(request.SortType, "asc", comparisonType: StringComparison.OrdinalIgnoreCase) ?
                mailReceives.OrderByPropertyName(request.Sort) : mailReceives.OrderByPropertyName(request.Sort, false);
        var listAsync = await mailReceives.ToListAsync(it => new CollectionProcessListDto(it.t1.MailId, it.t1.MailFrom, it.t1.MailNo, it.t1.MailPriority, it.t1.MailSubject, it.t1.MailTo,
            it.t1.Status, it.t1.MailDate, it.t4.AuditType, it.t4.AuditRemark, it.t4.AuditTime, it.t2.SortBy, it.t3.CurNodeId, it.t2.UndertakeUserId), cancellationToken);

        return new PageResult<CollectionProcessListDto>()
        {
            Data = await listAsync.ToAsyncEnumerable().SelectAwait(async receiveList =>
            {
                if (receiveList.SortByTemp is not null)
                {
                    ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository = userInfoRepository;
                    receiveList.SortBy = new { CnName = (await userBaseInfoRepository.GetCacheValueAsync(receiveList.SortByTemp))?.CnName ?? "", UserId = receiveList.SortByTemp };
                }

                if (receiveList.UndertakeUserTemp is not null)
                {
                    ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository = userInfoRepository;
                    receiveList.UndertakeUser = new { CnName = (await userBaseInfoRepository.GetCacheValueAsync(receiveList.UndertakeUserTemp))?.CnName ?? "", UserId = receiveList.UndertakeUserTemp };
                }   
                return receiveList;
            }).ToListAsync(cancellationToken: cancellationToken),
            Page = request.PageIndex.Value,
            PageSize = request.PageSize.Value,
            Total = count
        };
    }
}
